#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from psd_tools import PSDImage

# 设置控制台编码为UTF-8以支持emoji字符
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def rgba_to_hex(rgba):
    if isinstance(rgba, (list, tuple)):
        return '#{:02x}{:02x}{:02x}'.format(rgba[0], rgba[1], rgba[2])
    return '#000000'

psd_folder = 'psd_files'
output_folder = 'output'

ini_header_template = """
[template:0]
name = PSD自动生成模板
thumbnail = 

[template:0:cards:0]
background = {background}
"""

def process_psd(psd_path, base_output_folder):
    base_name = os.path.splitext(os.path.basename(psd_path))[0]
    psd_output_folder = os.path.join(base_output_folder, base_name)
    if not os.path.exists(psd_output_folder):
        os.makedirs(psd_output_folder)

    psd = PSDImage.open(psd_path)
    layers = [layer for layer in psd.descendants() if layer.is_visible() and not layer.is_group()]

    # 自动识别背景图层（最底层或名字含"背景"）
    background_layer = None
    for layer in layers:
        if '背景' in layer.name or 'background' in layer.name.lower():
            background_layer = layer
            break
    if not background_layer and layers:
        background_layer = layers[0]  # 默认最底层

    background_png = ''
    if background_layer:
        safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in background_layer.name])
        background_png = f'{base_name}_background_{safe_layer_name}.png'
        background_path = os.path.join(psd_output_folder, background_png)
        image = background_layer.composite()
        if image:
            image.save(background_path)

    # 生成缩略图
    thumbnail_name = f'{base_name}_thumbnail.png'
    thumbnail_path = os.path.join(psd_output_folder, thumbnail_name)
    
    # 创建缩略图（缩放到300x200）
    try:
        thumbnail = psd.composite()
        if thumbnail:
            thumbnail.thumbnail((300, 200))
            thumbnail.save(thumbnail_path)
            thumbnail_url = thumbnail_name
        else:
            thumbnail_url = ''
    except:
        thumbnail_url = ''
    
    ini_content = f"""[template]
name = {base_name}模板
thumbnail = {thumbnail_url}

[template:0:cards:0]
background = {background_png}

"""
    # 识别卡片组和未分组图层
    card_groups = []
    ungrouped_layers = []
    
    for layer in psd:
        if hasattr(layer, 'is_group') and layer.is_group():
            card_groups.append(layer)
            print(f"发现图层组: '{layer.name}' (包含 {len(list(layer))} 个子图层)")
        else:
            ungrouped_layers.append(layer)
    
    print(f"\n📊 多卡片结构分析:")
    print(f"  - 检测到 {len(card_groups)} 个图层组")
    print(f"  - 检测到 {len(ungrouped_layers)} 个未分组图层")
    
    if len(card_groups) == 0:
        print(f"  ⚠️ 未发现图层组，所有图层将作为单一卡片处理")
    else:
        print(f"  ✅ 将生成 {len(card_groups) + (1 if ungrouped_layers else 0)} 个卡片")
    
    # 如果没有组，则所有图层放在一个卡片中
    if not card_groups:
        card_groups = [None]  # None表示处理未分组的图层
        layers_to_process = [ungrouped_layers]
    else:
        layers_to_process = [list(group) for group in card_groups]
        if ungrouped_layers:
            layers_to_process.append(ungrouped_layers)
            card_groups.append(None)
    
    # 为每个卡片生成配置
    for card_index, (group, layers) in enumerate(zip(card_groups, layers_to_process)):
        if card_index > 0:  # 为额外的卡片添加配置
            ini_content += f"""[template:0:cards:{card_index}]
background = {background_png}

"""
        
        image_area_index = 0
        text_area_index = 0
        
        print(f"\n🔍 处理卡片 {card_index} 的图层 (共 {len(layers)} 个):")
        
        for layer in layers:
            print(f"  检查图层: '{layer.name}' (类型: {layer.kind}, 尺寸: {layer.width}x{layer.height}, 可见: {layer.is_visible()})")
            
            # 跳过已作为背景的图层
            if background_layer and layer == background_layer:
                print(f"    -> 📁 跳过: 背景图层")
                continue

            if layer.kind == 'type' and layer.is_visible():
                # 处理文字图层
                print(f"    -> 📝 处理文字图层")
                text = layer.text
                left, top = layer.left, layer.top
                width, height = layer.width, layer.height

                # 自动提取字体信息
                font = '楷体'
                font_size = 20
                try:
                    # 方法1: 通过engineData获取字体信息
                    if hasattr(layer, 'engine_data') and layer.engine_data:
                        engine_data = layer.engine_data
                        if 'DocumentResources' in engine_data and 'FontSet' in engine_data['DocumentResources']:
                            font_set = engine_data['DocumentResources']['FontSet']
                            if font_set:
                                first_font = font_set[0]
                                if 'Name' in first_font:
                                    font_name = first_font['Name']
                                    font_mapping = {
                                        'KaiTi': '楷体',
                                        'SimSun': '宋体', 
                                        'SimHei': '黑体',
                                        'Microsoft YaHei': '微软雅黑'
                                    }
                                    font = font_mapping.get(font_name, font_name)
                                if 'Size' in first_font:
                                    font_size = int(first_font['Size'])
                    
                    # 方法2: 通过text_data获取字体信息
                    if font == '楷体' and hasattr(layer, 'text_data') and layer.text_data:
                        text_data = layer.text_data
                        if 'EngineData' in text_data and 'DocumentResources' in text_data['EngineData']:
                            doc_res = text_data['EngineData']['DocumentResources']
                            if 'FontSet' in doc_res and doc_res['FontSet']:
                                first_font = doc_res['FontSet'][0]
                                if 'Name' in first_font:
                                    font_name = first_font['Name']
                                    font_mapping = {
                                        'KaiTi': '楷体',
                                        'SimSun': '宋体',
                                        'SimHei': '黑体', 
                                        'Microsoft YaHei': '微软雅黑'
                                    }
                                    font = font_mapping.get(font_name, font_name)
                    
                    # 方法3: 尝试从字体大小数据推断字体信息
                    if font == '楷体' and hasattr(layer, 'text_data') and layer.text_data:
                        if 'RunArray' in layer.text_data:
                            for run in layer.text_data['RunArray']:
                                if 'RunLengthArray' in run and 'StyleSheetData' in run:
                                    style_data = run['StyleSheetData']
                                    if 'FontSize' in style_data:
                                        font_size = int(style_data['FontSize'])
                                        break
                except Exception as e:
                    pass  # 使用默认值

                # 自动提取颜色
                color = '#000000'
                try:
                    if hasattr(layer, 'text_data') and layer.text_data:
                        if 'RunArray' in layer.text_data:
                            for run in layer.text_data['RunArray']:
                                if 'StyleSheetData' in run:
                                    style_data = run['StyleSheetData']
                                    if 'FillColor' in style_data:
                                        fill_color = style_data['FillColor']
                                        if 'Values' in fill_color and len(fill_color['Values']) >= 3:
                                            r = int(fill_color['Values'][1] * 255)
                                            g = int(fill_color['Values'][2] * 255)
                                            b = int(fill_color['Values'][3] * 255)
                                            color = f'#{r:02x}{g:02x}{b:02x}'
                                        break
                except Exception as e:
                    pass

                max_length = max(50, len(text) + 20)
                ini_content += f"""[template:0:cards:{card_index}:textAreas:{text_area_index}]
content = {text}
color = {color}
maxLength = {max_length}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}
font={font}
fontSize={font_size}
orientation=vertical

"""
                text_area_index += 1
                print(f"    -> ✅ 已添加文字区域: '{text[:20]}...'")
            elif layer.kind != 'type' and not layer.is_group():
                # 处理图片图层
                print(f"    -> 🖼️ 检查图像图层")
                
                if layer.width <= 10 or layer.height <= 10:
                    print(f"    -> ❌ 跳过: 图层尺寸太小 ({layer.width}x{layer.height})")
                    continue
                    
                if not layer.is_visible():
                    print(f"    -> ❌ 跳过: 图层不可见")
                    continue
                    
                try:
                    image = layer.composite()
                    if image:
                        safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in layer.name])
                        png_name = f'{base_name}_image_{image_area_index}_{safe_layer_name}.png'
                        png_path = os.path.join(psd_output_folder, png_name)
                        image.save(png_path)
                        left, top = layer.left, layer.top
                        width, height = layer.width, layer.height

                        ini_content += f"""[template:0:cards:{card_index}:imageAreas:{image_area_index}]
url = {png_name}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}

"""
                        image_area_index += 1
                        print(f"    -> ✅ 已处理图片图层: {layer.name} -> {png_name}")
                    else:
                        print(f"    -> ❌ 跳过: 无法生成图像")
                except Exception as e:
                    print(f"    -> ❌ 跳过: 处理图像时出错 - {str(e)}")
            else:
                if layer.is_group():
                    print(f"    -> 📁 跳过: 图层组")
                else:
                    print(f"    -> ⚠️ 跳过: 其他类型图层")

    ini_path = os.path.join(psd_output_folder, f"{base_name}.ini")
    with open(ini_path, 'w', encoding='utf-8') as f:
        f.write(ini_content)
    print(f"已生成 {ini_path}")

def batch_process_psd(psd_folder, output_folder):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    for file_name in os.listdir(psd_folder):
        if file_name.lower().endswith('.psd'):
            psd_path = os.path.join(psd_folder, file_name)
            process_psd(psd_path, output_folder)

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) == 3:
        # 命令行模式：python 123.py <psd_path> <output_folder>
        psd_path = os.path.abspath(sys.argv[1])
        output_folder = os.path.abspath(sys.argv[2])
        
        print(f"处理路径: {psd_path}")
        print(f"输出路径: {output_folder}")
        
        if os.path.isfile(psd_path) and psd_path.lower().endswith('.psd'):
            # 处理单个PSD文件
            process_psd(psd_path, output_folder)
            print(f"处理完成: {psd_path}")
        elif os.path.isdir(psd_path):
            # 处理文件夹中的所有PSD文件
            batch_process_psd(psd_path, output_folder)
            print(f"批量处理完成: {psd_path}")
        else:
            print(f"错误: 无效的PSD文件或文件夹路径: {psd_path}")
            print(f"文件存在: {os.path.exists(psd_path)}")
            print(f"是文件: {os.path.isfile(psd_path)}")
    else:
        # 默认模式：使用预设的文件夹路径
        batch_process_psd(psd_folder, output_folder)
