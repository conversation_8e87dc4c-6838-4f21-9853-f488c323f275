#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from psd_tools import PSDImage
import subprocess
import sys

class PSDProcessorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PSD文件处理工具 - GUI版本")
        self.root.geometry("900x700")
        self.root.configure(bg='#ffffff')
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', 
                       font=('Microsoft YaHei UI', 18, 'bold'),
                       foreground='#2c3e50',
                       background='#ffffff')
        
        style.configure('Heading.TLabel',
                       font=('Microsoft YaHei UI', 11, 'bold'),
                       foreground='#34495e',
                       background='#f8f9fa')
        
        style.configure('Custom.TLabelFrame',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground='#2c3e50',
                       background='#f8f9fa',
                       borderwidth=2,
                       relief='solid')
        
        # 创建主框架
        self.create_widgets()
        
        # 初始化变量
        self.psd_files = []
        self.output_folder = "output"
        
    def create_widgets(self):
        # 标题区域 - 使用渐变效果
        title_frame = tk.Frame(self.root, bg='#ffffff', height=80)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        # 添加装饰线
        top_line = tk.Frame(title_frame, bg='#3498db', height=4)
        top_line.pack(fill='x')
        
        title_content = tk.Frame(title_frame, bg='#ffffff')
        title_content.pack(fill='both', expand=True, pady=15)
        
        title_label = tk.Label(title_content, text="🎨 PSD文件处理工具", 
                              font=('Microsoft YaHei UI', 20, 'bold'), 
                              fg='#2c3e50', bg='#ffffff')
        title_label.pack()
        
        subtitle_label = tk.Label(title_content, text="专业的PSD文件分析与配置生成工具", 
                                 font=('Microsoft YaHei UI', 10), 
                                 fg='#7f8c8d', bg='#ffffff')
        subtitle_label.pack(pady=(5, 0))
        
        # 主容器 - 改善背景和间距
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=25, pady=20)
        
        # 文件选择区域 - 增强视觉效果
        file_frame = tk.LabelFrame(main_frame, text=" 📁 文件选择 ", 
                                  font=('Microsoft YaHei UI', 12, 'bold'),
                                  bg='#ffffff', fg='#2c3e50',
                                  bd=2, relief='solid')
        file_frame.pack(fill='x', pady=(0, 20))
        
        # 内部容器
        file_inner = tk.Frame(file_frame, bg='#ffffff')
        file_inner.pack(fill='x', padx=20, pady=15)
        
        # PSD文件选择
        psd_frame = tk.Frame(file_inner, bg='#ffffff')
        psd_frame.pack(fill='x', pady=(0, 15))
        
        psd_label = tk.Label(psd_frame, text="PSD文件:", 
                            font=('Microsoft YaHei UI', 10, 'bold'), 
                            bg='#ffffff', fg='#34495e')
        psd_label.pack(side='left')
        
        self.psd_path_var = tk.StringVar()
        psd_entry = tk.Entry(psd_frame, textvariable=self.psd_path_var, 
                            font=('Microsoft YaHei UI', 10), width=50,
                            bg='#ffffff', fg='#2c3e50',
                            bd=2, relief='solid',
                            insertbackground='#3498db')
        psd_entry.pack(side='left', padx=(15, 10), fill='x', expand=True)
        
        psd_btn = tk.Button(psd_frame, text="📂 选择文件", 
                           command=self.select_psd_file,
                           bg='#3498db', fg='white', 
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           relief='flat', padx=20, pady=8,
                           cursor='hand2',
                           activebackground='#2980b9',
                           activeforeground='white')
        psd_btn.pack(side='right')
        
        # 输出文件夹选择
        output_frame = tk.Frame(file_inner, bg='#ffffff')
        output_frame.pack(fill='x')
        
        output_label = tk.Label(output_frame, text="输出目录:", 
                               font=('Microsoft YaHei UI', 10, 'bold'), 
                               bg='#ffffff', fg='#34495e')
        output_label.pack(side='left')
        
        self.output_path_var = tk.StringVar(value="output")
        output_entry = tk.Entry(output_frame, textvariable=self.output_path_var, 
                               font=('Microsoft YaHei UI', 10), width=50,
                               bg='#ffffff', fg='#2c3e50',
                               bd=2, relief='solid',
                               insertbackground='#3498db')
        output_entry.pack(side='left', padx=(15, 10), fill='x', expand=True)
        
        output_btn = tk.Button(output_frame, text="📁 选择目录", 
                              command=self.select_output_folder,
                              bg='#3498db', fg='white', 
                              font=('Microsoft YaHei UI', 10, 'bold'),
                              relief='flat', padx=20, pady=8,
                              cursor='hand2',
                              activebackground='#2980b9',
                              activeforeground='white')
        output_btn.pack(side='right')
        
        # 操作按钮区域 - 改善按钮设计
        button_frame = tk.Frame(main_frame, bg='#f8f9fa')
        button_frame.pack(fill='x', pady=(0, 20))
        
        # 按钮容器
        btn_container = tk.Frame(button_frame, bg='#f8f9fa')
        btn_container.pack()
        
        # 分析按钮
        self.analyze_btn = tk.Button(btn_container, text="🔍 分析PSD图层", 
                                    command=self.analyze_psd,
                                    bg='#e74c3c', fg='white', 
                                    font=('Microsoft YaHei UI', 11, 'bold'),
                                    relief='flat', padx=25, pady=12,
                                    cursor='hand2',
                                    activebackground='#c0392b',
                                    activeforeground='white')
        self.analyze_btn.pack(side='left', padx=(0, 15))
        
        # 处理按钮
        self.process_btn = tk.Button(btn_container, text="⚙️ 生成INI配置", 
                                    command=self.process_psd,
                                    bg='#27ae60', fg='white', 
                                    font=('Microsoft YaHei UI', 11, 'bold'),
                                    relief='flat', padx=25, pady=12,
                                    cursor='hand2',
                                    activebackground='#229954',
                                    activeforeground='white')
        self.process_btn.pack(side='left', padx=(0, 15))
        
        # 批量处理按钮
        self.batch_btn = tk.Button(btn_container, text="📦 批量处理文件夹", 
                                  command=self.batch_process_folder,
                                  bg='#f39c12', fg='white', 
                                  font=('Microsoft YaHei UI', 11, 'bold'),
                                  relief='flat', padx=25, pady=12,
                                  cursor='hand2',
                                  activebackground='#e67e22',
                                  activeforeground='white')
        self.batch_btn.pack(side='left', padx=(0, 15))
        
        # 清空按钮
        clear_btn = tk.Button(btn_container, text="🗑️ 清空日志", 
                             command=self.clear_log,
                             bg='#95a5a6', fg='white', 
                             font=('Microsoft YaHei UI', 11, 'bold'),
                             relief='flat', padx=25, pady=12,
                             cursor='hand2',
                             activebackground='#7f8c8d',
                             activeforeground='white')
        clear_btn.pack(side='left')
        
        # 日志显示区域 - 改善日志界面
        log_frame = tk.LabelFrame(main_frame, text=" 📋 处理日志 ", 
                                 font=('Microsoft YaHei UI', 12, 'bold'),
                                 bg='#ffffff', fg='#2c3e50',
                                 bd=2, relief='solid')
        log_frame.pack(fill='both', expand=True)
        
        # 日志容器
        log_container = tk.Frame(log_frame, bg='#ffffff')
        log_container.pack(fill='both', expand=True, padx=15, pady=15)
        
        # 创建滚动文本框 - 改善字体和颜色
        self.log_text = scrolledtext.ScrolledText(log_container, 
                                                 font=('Consolas', 10),
                                                 bg='#1e1e1e', fg='#ffffff',
                                                 insertbackground='#ffffff',
                                                 selectbackground='#3498db',
                                                 selectforeground='#ffffff',
                                                 bd=0, relief='flat',
                                                 wrap=tk.WORD)
        self.log_text.pack(fill='both', expand=True)
        
        # 配置日志文本标签
        self.log_text.tag_configure('timestamp', foreground='#95a5a6')
        self.log_text.tag_configure('success', foreground='#2ecc71')
        self.log_text.tag_configure('error', foreground='#e74c3c')
        self.log_text.tag_configure('warning', foreground='#f39c12')
        self.log_text.tag_configure('info', foreground='#3498db')
        
        # 状态栏 - 改善状态栏设计
        status_frame = tk.Frame(self.root, bg='#2c3e50', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_var = tk.StringVar(value="🟢 就绪")
        status_label = tk.Label(status_frame, textvariable=self.status_var, 
                               font=('Microsoft YaHei UI', 9, 'bold'), 
                               fg='#ecf0f1', bg='#2c3e50')
        status_label.pack(side='left', padx=15, pady=5)
        
        # 版本信息
        version_label = tk.Label(status_frame, text="v2.0", 
                                font=('Microsoft YaHei UI', 8), 
                                fg='#95a5a6', bg='#2c3e50')
        version_label.pack(side='right', padx=15, pady=5)
        
        # 初始化日志
        self.log("🚀 PSD文件处理工具已启动", 'success')
        self.log("📝 请选择PSD文件开始处理...", 'info')
        
    def log(self, message, tag='normal'):
        """添加日志信息"""
        timestamp = self.get_timestamp()
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n", tag)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def get_timestamp(self):
        """获取当前时间戳"""
        import datetime
        return datetime.datetime.now().strftime("%H:%M:%S")
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("🗑️ 日志已清空", 'info')
        
    def select_psd_file(self):
        """选择PSD文件"""
        file_path = filedialog.askopenfilename(
            title="选择PSD文件",
            filetypes=[("PSD文件", "*.psd"), ("所有文件", "*.*")]
        )
        if file_path:
            self.psd_path_var.set(file_path)
            self.log(f"📂 已选择PSD文件: {os.path.basename(file_path)}", 'success')
            self.status_var.set(f"📂 已选择: {os.path.basename(file_path)}")
            
    def batch_process_folder(self):
        """选择文件夹进行批量处理"""
        folder_path = filedialog.askdirectory(title="选择包含PSD文件的文件夹")
        if folder_path:
            # 检查文件夹中是否有PSD文件
            psd_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.psd')]
            if not psd_files:
                messagebox.showwarning("警告", "所选文件夹中没有找到PSD文件")
                return
                
            self.psd_path_var.set(folder_path)
            self.log(f"📁 已选择文件夹: {folder_path}", 'success')
            self.log(f"🔍 找到 {len(psd_files)} 个PSD文件: {', '.join(psd_files)}", 'info')
            self.status_var.set(f"📁 已选择文件夹: {len(psd_files)} 个PSD文件")
            
            # 自动开始批量处理
            output_path = self.output_path_var.get()
            if not output_path:
                messagebox.showerror("错误", "请设置输出目录")
                return
                
            self.status_var.set("⚙️ 正在批量处理PSD文件...")
            self.batch_btn.config(state='disabled')
            
            thread = threading.Thread(target=self._batch_process_psd_thread, args=(folder_path, output_path))
            thread.daemon = True
            thread.start()
            
    def select_output_folder(self):
        """选择输出文件夹"""
        folder_path = filedialog.askdirectory(title="选择输出目录")
        if folder_path:
            self.output_path_var.set(folder_path)
            self.log(f"📁 输出目录设置为: {folder_path}", 'success')
            
    def analyze_psd(self):
        """分析PSD文件"""
        psd_path = self.psd_path_var.get()
        if not psd_path or not os.path.exists(psd_path):
            messagebox.showerror("错误", "请先选择有效的PSD文件")
            return
            
        self.status_var.set("🔍 正在分析PSD文件...")
        self.analyze_btn.config(state='disabled')
        
        # 在新线程中执行分析
        thread = threading.Thread(target=self._analyze_psd_thread, args=(psd_path,))
        thread.daemon = True
        thread.start()
        
    def _analyze_psd_thread(self, psd_path):
        """在线程中分析PSD文件"""
        try:
            self.log(f"🔍 开始分析PSD文件: {os.path.basename(psd_path)}", 'info')
            
            psd = PSDImage.open(psd_path)
            self.log(f"📐 PSD尺寸: {psd.width} x {psd.height} 像素", 'info')
            self.log(f"🎨 颜色模式: {psd.color_mode}", 'info')
            
            layers = list(psd.descendants())
            visible_layers = [layer for layer in layers if layer.is_visible()]
            self.log(f"📊 总图层数: {len(layers)} (可见: {len(visible_layers)})", 'info')
            
            text_layers = [layer for layer in layers if layer.kind == 'type' and layer.is_visible()]
            image_layers = [layer for layer in layers if layer.kind != 'type' and not layer.is_group() and layer.is_visible()]
            groups = [layer for layer in layers if layer.is_group()]
            
            self.log(f"")
            self.log(f"📝 文字图层: {len(text_layers)} 个", 'info')
            self.log(f"🖼️ 图像图层: {len(image_layers)} 个", 'info')
            self.log(f"📁 图层组: {len(groups)} 个", 'info')
            
            # 分析多卡片结构
            card_groups = []
            ungrouped_layers = []
            
            for layer in psd:
                if hasattr(layer, 'is_group') and layer.is_group():
                    card_groups.append(layer)
                else:
                    ungrouped_layers.append(layer)
            
            self.log(f"")
            self.log(f"🃏 多卡片结构分析:", 'warning')
            if card_groups:
                self.log(f"  - 检测到 {len(card_groups)} 个卡片组", 'success')
                for i, group in enumerate(card_groups):
                    group_layers = list(group)
                    group_text_layers = [l for l in group_layers if l.kind == 'type' and l.is_visible()]
                    group_image_layers = [l for l in group_layers if l.kind != 'type' and not l.is_group() and l.is_visible()]
                    self.log(f"    卡片 {i+1}: '{group.name}' (文本:{len(group_text_layers)}, 图像:{len(group_image_layers)})", 'info')
                if ungrouped_layers:
                    ungrouped_visible = [l for l in ungrouped_layers if l.is_visible() and not l.is_group()]
                    self.log(f"  - 未分组图层: {len(ungrouped_visible)} 个 (将作为额外卡片)", 'warning')
            else:
                self.log(f"  - 未检测到图层组，所有图层将作为单一卡片处理", 'warning')
            
            # 分析背景图层
            background_candidates = []
            for layer in image_layers:
                if '背景' in layer.name or 'background' in layer.name.lower():
                    background_candidates.append(layer)
            
            self.log(f"")
            self.log(f"🖼️ 背景图层分析:", 'warning')
            if background_candidates:
                self.log(f"  - 检测到 {len(background_candidates)} 个背景候选图层", 'success')
                for bg in background_candidates:
                    self.log(f"    '{bg.name}' ({bg.width}x{bg.height})", 'info')
            else:
                if image_layers:
                    self.log(f"  - 未找到明确的背景图层，将使用第一个图像图层: '{image_layers[0].name}'", 'warning')
                else:
                    self.log(f"  - 未找到背景图层", 'error')
            
            self.log(f"")
            self.log(f"📋 === 详细图层信息 ===", 'warning')
            
            layer_count = 0
            for layer in visible_layers:
                layer_count += 1
                if layer_count > 20:  # 限制显示数量
                    remaining = len(visible_layers) - 20
                    self.log(f"... 还有 {remaining} 个图层未显示", 'warning')
                    break
                    
                layer_type = "📁 图层组" if layer.is_group() else ("📝 文字图层" if layer.kind == 'type' else "🖼️ 图像图层")
                self.log(f"{layer_count}. {layer.name} ({layer_type})", 'info')
                self.log(f"   📍 位置: ({layer.left}, {layer.top})")
                self.log(f"   📏 尺寸: {layer.width} x {layer.height}")
                
                if layer.kind == 'type':
                    try:
                        text_content = layer.text[:30] + "..." if len(layer.text) > 30 else layer.text
                        self.log(f"   💬 文本: '{text_content}'", 'info')
                        
                        # 尝试获取字体信息
                        try:
                            if hasattr(layer, 'text_data') and layer.text_data:
                                if 'RunArray' in layer.text_data:
                                    for run in layer.text_data['RunArray']:
                                        if 'StyleSheetData' in run:
                                            style_data = run['StyleSheetData']
                                            if 'FontSize' in style_data:
                                                font_size = int(style_data['FontSize'])
                                                self.log(f"   🔤 字体大小: {font_size}px", 'info')
                                                break
                        except:
                            pass
                    except:
                        self.log(f"   💬 文本: [无法读取]", 'error')
                elif not layer.is_group() and layer.width > 10 and layer.height > 10:
                    self.log(f"   ✅ 符合图像区域条件 (尺寸 > 10x10)", 'success')
                elif not layer.is_group():
                    self.log(f"   ⚠️ 图层过小，将被过滤", 'warning')
                        
            self.log(f"")
            self.log(f"✅ PSD文件分析完成!", 'success')
            self.log(f"🎯 预计生成结果:", 'warning')
            self.log(f"  - INI配置文件: 1 个", 'info')
            self.log(f"  - 缩略图: 1 个", 'info')
            self.log(f"  - 背景图: 1 个", 'info')
            valid_image_layers = [l for l in image_layers if l.width > 10 and l.height > 10]
            self.log(f"  - 图像区域: {len(valid_image_layers)} 个", 'info')
            self.log(f"  - 文本区域: {len(text_layers)} 个", 'info')
            
        except Exception as e:
            self.log(f"❌ 分析PSD文件时出错: {str(e)}", 'error')
        finally:
            self.root.after(0, lambda: (
                self.analyze_btn.config(state='normal'),
                self.status_var.set("✅ 分析完成")
            ))
            
    def process_psd(self):
        """处理PSD文件生成INI"""
        psd_path = self.psd_path_var.get()
        output_path = self.output_path_var.get()
        
        if not psd_path or not os.path.exists(psd_path):
            messagebox.showerror("错误", "请先选择有效的PSD文件")
            return
            
        if not output_path:
            messagebox.showerror("错误", "请设置输出目录")
            return
            
        self.status_var.set("正在处理PSD文件...")
        self.process_btn.config(state='disabled')
        
        # 检查是否为文件夹批量处理
        if os.path.isdir(psd_path):
            # 批量处理文件夹中的PSD文件
            thread = threading.Thread(target=self._batch_process_psd_thread, args=(psd_path, output_path))
        else:
            # 单个文件处理
            thread = threading.Thread(target=self._process_psd_thread, args=(psd_path, output_path))
        thread.daemon = True
        thread.start()
        
    def _process_psd_thread(self, psd_path, output_path):
        """在线程中处理PSD文件"""
        try:
            self.log(f"⚙️ 开始处理PSD文件: {os.path.basename(psd_path)}", 'info')
            
            # 使用现有的123.py脚本进行处理
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '123.py')
            if not os.path.exists(script_path):
                self.log("❌ 错误: 找不到123.py处理脚本", 'error')
                return
                
            # 调用123.py脚本处理单个文件
            result = subprocess.run([sys.executable, script_path, psd_path, output_path], 
                                  capture_output=True, text=True, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                base_name = os.path.splitext(os.path.basename(psd_path))[0]
                ini_path = os.path.join(output_path, base_name, f"{base_name}.ini")
                
                # 显示详细的处理结果
                self.log(f"✅ INI配置文件已生成: {ini_path}", 'success')
                
                # 检查生成的文件
                output_dir = os.path.join(output_path, base_name)
                if os.path.exists(output_dir):
                    files = os.listdir(output_dir)
                    ini_files = [f for f in files if f.endswith('.ini')]
                    png_files = [f for f in files if f.endswith('.png')]
                    
                    self.log(f"📊 生成文件统计:", 'warning')
                    self.log(f"  - INI配置文件: {len(ini_files)} 个", 'info')
                    self.log(f"  - PNG图片文件: {len(png_files)} 个", 'info')
                    
                    if png_files:
                        thumbnail_files = [f for f in png_files if 'thumbnail' in f]
                        background_files = [f for f in png_files if 'background' in f]
                        image_files = [f for f in png_files if 'image' in f]
                        
                        if thumbnail_files:
                            self.log(f"  - 缩略图: {', '.join(thumbnail_files)}", 'success')
                        if background_files:
                            self.log(f"  - 背景图: {', '.join(background_files)}", 'success')
                        if image_files:
                            self.log(f"  - 图像区域: {len(image_files)} 个", 'success')
                
                # 显示输出信息
                if result.stdout:
                    output_lines = result.stdout.strip().split('\n')
                    for line in output_lines:
                        if line.strip():
                            self.log(f"📝 处理信息: {line.strip()}", 'info')
                
                self.log("✅ 处理完成! 支持功能:", 'success')
                self.log("  - 多卡片结构支持", 'info')
                self.log("  - 文本区域自动识别(字体、颜色、大小)", 'info')
                self.log("  - 图像区域自动提取", 'info')
                self.log("  - 缩略图自动生成", 'info')
                
                # 询问是否打开输出文件夹
                self.root.after(0, lambda: self._ask_open_folder(output_dir))
                
            else:
                self.log(f"❌ 处理失败，返回码: {result.returncode}", 'error')
                if result.stderr:
                    error_lines = result.stderr.strip().split('\n')
                    for line in error_lines:
                        if line.strip():
                            self.log(f"❌ 错误: {line.strip()}", 'error')
                            
        except Exception as e:
            self.log(f"❌ 处理PSD文件时出错: {str(e)}", 'error')
        finally:
            self.root.after(0, lambda: (
                self.process_btn.config(state='normal'),
                self.status_var.set("✅ 处理完成")
            ))
            
    def _batch_process_psd_thread(self, folder_path, output_path):
        """在线程中批量处理PSD文件"""
        try:
            psd_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.psd')]
            total_files = len(psd_files)
            
            self.log(f"🚀 开始批量处理 {total_files} 个PSD文件", 'warning')
            
            success_count = 0
            failed_count = 0
            
            for i, psd_file in enumerate(psd_files, 1):
                psd_path = os.path.join(folder_path, psd_file)
                
                self.log(f"⚙️ [{i}/{total_files}] 处理: {psd_file}", 'info')
                self.root.after(0, lambda p=i, t=total_files: self.status_var.set(f"⚙️ 处理中 ({p}/{t}): {psd_file}"))
                
                try:
                    # 使用现有的123.py脚本进行处理
                    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '123.py')
                    result = subprocess.run([sys.executable, script_path, psd_path, output_path], 
                                          capture_output=True, text=True, encoding='utf-8', errors='ignore')
                    
                    if result.returncode == 0:
                        base_name = os.path.splitext(psd_file)[0]
                        output_dir = os.path.join(output_path, base_name)
                        
                        # 检查生成的文件
                        if os.path.exists(output_dir):
                            files = os.listdir(output_dir)
                            ini_files = [f for f in files if f.endswith('.ini')]
                            png_files = [f for f in files if f.endswith('.png')]
                            image_files = [f for f in png_files if 'image' in f]
                            
                            self.log(f"  ✅ 成功: INI({len(ini_files)}) PNG({len(png_files)}) 图像区域({len(image_files)})", 'success')
                            success_count += 1
                        else:
                            self.log(f"  ⚠️ 警告: 输出目录未创建", 'warning')
                            failed_count += 1
                    else:
                        self.log(f"  ❌ 失败: 返回码 {result.returncode}", 'error')
                        if result.stderr:
                            error_msg = result.stderr.strip().split('\n')[0]  # 只显示第一行错误
                            self.log(f"    错误信息: {error_msg}", 'error')
                        failed_count += 1
                        
                except Exception as e:
                    self.log(f"  ❌ 异常: {str(e)}", 'error')
                    failed_count += 1
            
            # 显示批量处理总结
            self.log(f"")
            self.log(f"🎯 批量处理完成!", 'success')
            self.log(f"📊 处理统计:", 'warning')
            self.log(f"  - 总文件数: {total_files}", 'info')
            self.log(f"  - 成功处理: {success_count}", 'success')
            self.log(f"  - 处理失败: {failed_count}", 'error' if failed_count > 0 else 'info')
            self.log(f"  - 成功率: {success_count/total_files*100:.1f}%", 'success' if success_count == total_files else 'warning')
            
            self.log(f"")
            self.log(f"✨ 支持的功能特性:", 'warning')
            self.log(f"  - 🃏 多卡片结构识别", 'info')
            self.log(f"  - 📝 文本区域提取(字体、颜色、大小)", 'info')
            self.log(f"  - 🖼️ 图像区域自动提取", 'info')
            self.log(f"  - 🖼️ 缩略图和背景图生成", 'info')
            
            # 询问是否打开输出文件夹
            self.root.after(0, lambda: self._ask_open_folder(output_path))
            
        except Exception as e:
            self.log(f"❌ 批量处理时出错: {str(e)}", 'error')
        finally:
            self.root.after(0, lambda: (
                self.batch_btn.config(state='normal'),
                self.status_var.set(f"✅ 批量处理完成 (成功:{success_count}, 失败:{failed_count})")
            ))
            
    def _ask_open_folder(self, folder_path):
        """询问是否打开输出文件夹"""
        if messagebox.askyesno("处理完成", f"文件处理完成!\n\n是否打开输出文件夹?\n{folder_path}"):
            try:
                os.startfile(folder_path)  # Windows
            except:
                try:
                    subprocess.run(['open', folder_path])  # macOS
                except:
                    try:
                        subprocess.run(['xdg-open', folder_path])  # Linux
                    except:
                        self.log(f"⚠️ 无法自动打开文件夹: {folder_path}", 'warning')

def main():
    root = tk.Tk()
    app = PSDProcessorGUI(root)
    
    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()